/* CSS Custom Properties (Variables) */
:root {
    /* Color Palette */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    --secondary-50: #f0fdf4;
    --secondary-100: #dcfce7;
    --secondary-200: #bbf7d0;
    --secondary-300: #86efac;
    --secondary-400: #4ade80;
    --secondary-500: #22c55e;
    --secondary-600: #16a34a;
    --secondary-700: #15803d;
    --secondary-800: #166534;
    --secondary-900: #14532d;

    --neutral-50: #fafafa;
    --neutral-100: #f5f5f5;
    --neutral-200: #e5e5e5;
    --neutral-300: #d4d4d4;
    --neutral-400: #a3a3a3;
    --neutral-500: #737373;
    --neutral-600: #525252;
    --neutral-700: #404040;
    --neutral-800: #262626;
    --neutral-900: #171717;

    --success-50: #f0fdf4;
    --success-100: #dcfce7;
    --success-200: #bbf7d0;
    --success-300: #86efac;
    --success-400: #4ade80;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;
    --success-800: #166534;
    --success-900: #14532d;

    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-200: #fde68a;
    --warning-300: #fcd34d;
    --warning-400: #fbbf24;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;
    --warning-800: #92400e;
    --warning-900: #78350f;

    --error-50: #fef2f2;
    --error-100: #fee2e2;
    --error-200: #fecaca;
    --error-300: #fca5a5;
    --error-400: #f87171;
    --error-500: #ef4444;
    --error-600: #dc2626;
    --error-700: #b91c1c;
    --error-800: #991b1b;
    --error-900: #7f1d1d;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    --gradient-secondary: linear-gradient(135deg, #22c55e 0%, #15803d 100%);
    --gradient-success: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --gradient-error: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;

    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Typography */
    --font-sans: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-serif: 'Cambria', Georgia, serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

    /* Z-Index Layer Management System - Enhanced Hierarchy */
    --z-sticky-header: 100;
    --z-menu-bar: 200;
    --z-dropdown-menus: 2000;
    --z-header-dropdowns: 2500;
    --z-tooltips: 3000;
    --z-search-results: 4000;

    --z-floating-buttons: 6000;
    --z-sidebar-overlay: 7000;
    --z-sidebars: 8000;
    --z-modals: 9000;
    --z-notifications: 10000;
    /* Additional layers for menu system */
    --z-mega-menu-overlay: 1999;
    --z-menu-backdrop: 1998;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cambria', serif;
    background-color: #ffffff;
    color: #2c2c2c;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

#root {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Redesigned Header */
.dashboard-header {
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
    color: #ffffff;
    padding: 0.75rem 0;
    border-bottom: 1px solid #333333;
    position: sticky;
    top: 0;
    z-index: var(--z-sticky-header);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: none;
    margin: 0;
    padding: 0 2rem;
    gap: 2rem;
    min-height: 70px;
}

/* Left Section - Company Branding */
.header-left {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.brand-section {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.brand-section:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-1px);
}

.header-logo {
    height: 45px;
    width: auto;
    filter: brightness(1.1);
}

.brand-text {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.brand-title {
    font-family: 'Cambria', serif;
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0;
    line-height: 1;
    background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-subtitle {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.header-subtitle {
    font-size: 0.875rem;
    color: #cccccc;
    font-weight: 400;
}

/* Center Section - Search Functionality */
.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 700px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Enhanced Search Container */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.08));
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 25px;
    backdrop-filter: blur(15px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    width: 100%;
    max-width: 600px;
    overflow: hidden;
}

.search-container::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.search-container:hover::before {
    opacity: 1;
}

.search-container:focus-within {
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.3),
        0 0 0 3px rgba(255, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.18), rgba(255, 255, 255, 0.12));
}

/* Enhanced Search Input */
.search-input {
    border: none;
    background: transparent;
    color: #ffffff;
    font-size: 1rem;
    flex: 1;
    outline: none;
    min-width: 0;
    padding: 1rem 1.25rem;
    font-family: 'Cambria', serif;
    font-weight: 500;
    letter-spacing: 0.3px;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.65);
    font-style: italic;
    font-weight: 400;
}

.search-icon {
    color: rgba(255, 255, 255, 0.9);
    margin-left: 1.25rem;
    margin-right: 0.75rem;
    font-size: 22px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.search-container:focus-within .search-icon {
    color: #ffffff;
    transform: scale(1.1);
}

.search-shortcut {
    margin-right: 1.25rem;
    flex-shrink: 0;
}

.search-shortcut span {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.9);
    padding: 0.375rem 0.75rem;
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
}

.search-container:focus-within .search-shortcut span {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.2);
}

/* Search Results Overlay and Panel */
.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: var(--z-search-results);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.search-overlay.active {
    opacity: 1;
    visibility: visible;
}

.search-results {
    position: fixed;
    top: 120px;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 700px;
    max-height: 70vh;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 249, 250, 0.95));
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    backdrop-filter: blur(20px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    z-index: calc(var(--z-search-results) + 1);
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%) translateY(-20px) scale(0.95);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.search-results.active {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0) scale(1);
}

.search-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 1.5rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.02), rgba(0, 0, 0, 0.01));
}

.search-results-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #2c2c2c;
    font-weight: 600;
}

.results-count {
    font-size: 0.9rem;
    color: rgba(0, 0, 0, 0.7);
}

.search-term {
    font-size: 0.9rem;
    color: #2c2c2c;
}

.search-results-close {
    background: none;
    border: none;
    color: rgba(0, 0, 0, 0.6);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.search-results-close:hover {
    background: rgba(0, 0, 0, 0.05);
    color: #2c2c2c;
}

.search-results-content {
    padding: 1rem 1.5rem 1.5rem;
    max-height: calc(70vh - 100px);
    overflow-y: auto;
}

.search-section {
    margin-bottom: 2rem;
}

.search-section:last-child {
    margin-bottom: 0;
}

.search-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.search-section-header h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #2c2c2c;
    margin: 0;
}

.clear-recent {
    background: none;
    border: none;
    color: rgba(0, 0, 0, 0.6);
    cursor: pointer;
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.clear-recent:hover {
    background: rgba(0, 0, 0, 0.05);
    color: #2c2c2c;
}

.search-items,
.recent-searches {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.search-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    width: 100%;
}

.search-item:hover {
    background: rgba(0, 0, 0, 0.03);
    border-color: rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.search-item.selected {
    background: rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.15);
}

.search-item-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.08), rgba(0, 0, 0, 0.04));
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.search-item-icon .material-icons {
    font-size: 20px;
    color: rgba(0, 0, 0, 0.7);
}

.search-item-content {
    flex: 1;
    min-width: 0;
}

.search-item-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2c2c2c;
    margin-bottom: 0.25rem;
    line-height: 1.3;
}

.search-item-description {
    font-size: 0.8rem;
    color: rgba(0, 0, 0, 0.6);
    line-height: 1.3;
}

.search-item-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.search-item-category {
    font-size: 0.75rem;
    color: rgba(0, 0, 0, 0.5);
    background: rgba(0, 0, 0, 0.05);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-weight: 500;
}

.search-no-results {
    text-align: center;
    padding: 3rem 1rem;
    color: rgba(0, 0, 0, 0.6);
}

.no-results-icon {
    margin-bottom: 1rem;
}

.no-results-icon .material-icons {
    font-size: 48px;
    color: rgba(0, 0, 0, 0.3);
}

.search-no-results h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c2c2c;
    margin-bottom: 0.5rem;
}

.search-no-results p {
    font-size: 0.9rem;
    color: rgba(0, 0, 0, 0.6);
    margin: 0;
}

.search-suggestions {
    margin-top: 2rem;
}

.search-suggestions h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2c2c2c;
    margin-bottom: 1rem;
}

.suggestion-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.suggestion-tag {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #2c2c2c;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.suggestion-tag:hover {
    background: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

/* Highlight text in search results */
.search-highlight {
    background: rgba(255, 193, 7, 0.3);
    color: #2c2c2c;
    font-weight: 600;
    padding: 0.1rem 0.2rem;
    border-radius: 3px;
}

/* Old search styles removed */

/* More old search styles removed */

/* Right Section - Controls and Navigation */
.header-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-shrink: 0;
}

/* Modern Branch Selector */
.branch-selector-modern {
    position: relative;
}

.branch-selector-button {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    color: #ffffff;
    padding: 0.75rem 1.25rem;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    min-width: 180px;
    font-family: 'Cambria', serif;
    position: relative;
    overflow: hidden;
}

.branch-selector-button::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.branch-selector-button:hover::before {
    opacity: 1;
}

.branch-selector-button:hover {
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
}

.branch-selector-button.active {
    border-color: rgba(255, 255, 255, 0.4);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
}

.branch-icon {
    font-size: 20px;
    color: rgba(255, 255, 255, 0.9);
}

.branch-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex: 1;
}

.branch-label {
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
    line-height: 1;
}

.branch-name {
    font-size: 0.9rem;
    color: #ffffff;
    font-weight: 600;
    line-height: 1.2;
    margin-top: 0.125rem;
}

.branch-selector-button .dropdown-arrow {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.8);
    transition: transform 0.3s ease;
}

.branch-selector-button.active .dropdown-arrow {
    transform: rotate(180deg);
}

/* Enhanced Branch Dropdown Menu - Improved Size and Contrast */
.branch-menu {
    position: absolute;
    top: calc(100% + 12px);
    left: 0;
    right: 0;
    background: #ffffff;
    border: 2px solid rgba(0, 0, 0, 0.15);
    border-radius: 16px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(0, 0, 0, 0.08);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.95);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: var(--z-header-dropdowns);
    min-width: 400px;
    max-width: 480px;
    overflow: visible;
    max-height: none;
}

.branch-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

/* Branch Dropdown Search - Improved Size and Contrast */
.branch-search-container {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.15);
    background: #f8f9fa;
}

.branch-search-input {
    width: 100%;
    padding: 1rem 1.25rem 1rem 3rem;
    border: 2px solid rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    background: #ffffff;
    color: #1a1a1a;
    font-size: 1rem;
    font-family: 'Cambria', serif;
    outline: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.branch-search-input:focus {
    border-color: #2563eb;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.branch-search-input::placeholder {
    color: rgba(0, 0, 0, 0.6);
    font-style: italic;
    font-weight: 400;
}

.branch-search-icon {
    position: absolute;
    left: 2.25rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(0, 0, 0, 0.7);
    font-size: 20px;
    pointer-events: none;
}

.branch-menu .dropdown-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1.25rem 1.5rem 0.75rem;
    background: transparent;
    border-bottom: none;
    color: #1a1a1a;
    font-weight: 600;
    font-size: 1rem;
}

.branch-menu .dropdown-header .material-icons {
    font-size: 22px;
    color: #2563eb;
}

.branch-menu .dropdown-item {
    display: flex;
    align-items: center;
    gap: 1.25rem;
    padding: 1.25rem 1.5rem;
    color: #1a1a1a;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    position: relative;
    min-height: 60px;
}

.branch-menu .dropdown-item:last-child {
    border-bottom: none;
}

.branch-menu .dropdown-item:hover {
    background: #f1f5f9;
    color: #0f172a;
    transform: translateX(4px);
}

.branch-menu .dropdown-item.active {
    background: #e0f2fe;
    color: #0f172a;
    border-left: 4px solid #2563eb;
}

.branch-menu .dropdown-item.hidden {
    display: none;
}

.branch-menu .dropdown-item .material-icons:first-child {
    font-size: 22px;
    color: #64748b;
}

.branch-item-info {
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 0.25rem;
}

.branch-item-name {
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.3;
    color: #1a1a1a;
}

.branch-item-code {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
}

.check-icon {
    font-size: 20px;
    color: #94a3b8;
    transition: all 0.3s ease;
}

.branch-menu .dropdown-item.active .check-icon {
    color: #22c55e;
}

.branch-menu .dropdown-item:hover .check-icon {
    color: #64748b;
}

/* No Results Message - Improved Contrast */
.branch-no-results {
    padding: 2.5rem 1.5rem;
    text-align: center;
    color: #64748b;
    font-style: italic;
    font-size: 1rem;
    font-weight: 500;
}

/* Control Buttons Group */
.control-buttons-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.control-button {
    position: relative;
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: #ffffff;
    border-radius: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(5px);
}

.control-button:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.control-button .material-icons {
    font-size: 20px;
}

/* Special Button Variants */
.month-end-button {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(76, 175, 80, 0.1));
    border-color: rgba(76, 175, 80, 0.3);
}

.month-end-button:hover {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.2));
    border-color: rgba(76, 175, 80, 0.5);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.2);
}

/* Enhanced Notification Badge */
.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #ff4444, #cc0000);
    color: #ffffff;
    font-size: 0.7rem;
    font-weight: 700;
    padding: 3px 7px;
    border-radius: 12px;
    min-width: 18px;
    text-align: center;
    line-height: 1;
    box-shadow:
        0 3px 8px rgba(255, 68, 68, 0.4),
        0 0 0 2px rgba(0, 0, 0, 0.8);
    animation: pulse 2s infinite;
    font-family: 'Roboto', sans-serif;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow:
            0 3px 8px rgba(255, 68, 68, 0.4),
            0 0 0 2px rgba(0, 0, 0, 0.8);
    }
    50% {
        transform: scale(1.1);
        box-shadow:
            0 4px 12px rgba(255, 68, 68, 0.6),
            0 0 0 2px rgba(0, 0, 0, 0.8);
    }
    100% {
        transform: scale(1);
        box-shadow:
            0 3px 8px rgba(255, 68, 68, 0.4),
            0 0 0 2px rgba(0, 0, 0, 0.8);
    }
}

/* User Section */
.user-section {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding-left: 1rem;
    border-left: 1px solid rgba(255, 255, 255, 0.15);
}

/* Simple User Display */
.user-display {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 2px solid rgba(255, 255, 255, 0.15);
    color: #ffffff;
    padding: 0.75rem 1.25rem;
    border-radius: 16px;
    font-family: 'Cambria', serif;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.user-display::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), transparent);
    opacity: 1;
    transition: opacity 0.3s ease;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(255, 255, 255, 0.2);
    flex-shrink: 0;
}

.user-avatar .material-icons {
    font-size: 24px;
    color: rgba(255, 255, 255, 0.9);
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-name {
    font-weight: 600;
    font-size: 0.95rem;
    line-height: 1.2;
    color: #ffffff;
}



/* Enhanced Logout Button */
.logout-button {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.15), rgba(244, 67, 54, 0.1));
    border: 2px solid rgba(244, 67, 54, 0.3);
    color: #ffffff;
    border-radius: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.logout-button::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.logout-button:hover::before {
    opacity: 1;
}

.logout-button:hover {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.25), rgba(244, 67, 54, 0.15));
    border-color: rgba(244, 67, 54, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(244, 67, 54, 0.3);
}

.logout-button .material-icons {
    font-size: 22px;
}

/* Tooltips */
[data-tooltip] {
    position: relative;
}

[data-tooltip]:hover::before {
    content: attr(data-tooltip);
    position: absolute;
    top: calc(100% + 8px);
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.9);
    color: #ffffff;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-family: 'Cambria', serif;
    white-space: nowrap;
    z-index: var(--z-tooltips);
    opacity: 0;
    animation: tooltipFadeIn 0.3s ease forwards;
}

[data-tooltip]:hover::after {
    content: '';
    position: absolute;
    top: calc(100% + 2px);
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-bottom-color: rgba(0, 0, 0, 0.9);
    z-index: var(--z-tooltips);
    opacity: 0;
    animation: tooltipFadeIn 0.3s ease forwards;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* Header Dropdowns */
.header-dropdown {
    position: relative;
}

.dropdown-arrow {
    transition: transform 0.3s ease;
    font-size: 16px;
}

.icon-button.active .dropdown-arrow {
    transform: rotate(180deg);
}

.icon-button.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

/* Header Dropdown Menus - Higher z-index than menu bar */
.header-dropdown .dropdown-menu {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    z-index: var(--z-header-dropdowns);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    max-height: 300px;
    overflow-y: auto;
}

/* Ensure all header dropdowns appear above menu bar */
.header-dropdown {
    z-index: var(--z-header-dropdowns);
}

/* General Dropdown Menus */
.dropdown-menu {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    z-index: var(--z-dropdown-menus);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #2c2c2c;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item.active {
    background-color: #e3f2fd;
    color: #1976d2;
}

.dropdown-item .material-icons {
    font-size: 18px;
}

.dropdown-separator {
    height: 1px;
    background-color: #e0e0e0;
    margin: 0.5rem 0;
}

.logout-item {
    color: #d32f2f;
}

.logout-item:hover {
    background-color: #ffebee;
}



/* Branch Selector Specific - Ensure it appears above menu bar */
.branch-selector .dropdown-menu {
    left: 0;
    right: auto;
    min-width: 180px;
    max-width: 250px;
    white-space: nowrap;
    z-index: var(--z-header-dropdowns);
}

.branch-selector .dropdown-item {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.branch-selector .dropdown-item .material-icons {
    margin-right: 0.5rem;
    font-size: 18px;
    color: #666;
}

.branch-selector .dropdown-item.active .material-icons {
    color: #1976d2;
}

/* Horizontal Navigation - REMOVED */

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 160px); /* Account for header and footer */
    background-color: #ffffff;
}

/* Content Area */
.content-area {
    flex: 1;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e0e0e0;
    position: sticky;
    top: 80px; /* Stick below header only (no menu bar) */
    background-color: #ffffff;
    z-index: var(--z-dashboard-content);
    gap: 2rem;
}

.header-left {
    flex: 1;
}

.page-title {
    font-family: 'Cambria', serif;
    font-size: 2rem;
    font-weight: 700;
    color: #2c2c2c;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666666;
    font-size: 0.875rem;
}

.breadcrumb .material-icons {
    font-size: 16px;
}



















/* Smooth Transitions */
* {
    transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}



/* Footer - Single Horizontal Row Design */
.dashboard-footer {
    background-color: #000000;
    color: #ffffff;
    padding: 1rem 2rem;
    border-top: 1px solid #333333;
    margin-top: auto;
    min-height: 60px;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.footer-left,
.footer-center,
.footer-right {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
}

.footer-left {
    color: #cccccc;
    flex-shrink: 0;
}

.footer-center {
    color: #999999;
    justify-content: center;
    flex: 1;
}

.footer-right {
    color: #cccccc;
    flex-shrink: 0;
}

.footer-version,
.footer-build {
    font-size: 0.8rem;
    color: #888888;
}

.footer-link {
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    color: #cccccc;
    text-decoration: none;
    transition: color 0.3s ease;
    white-space: nowrap;
}

.footer-link:hover {
    color: #ffffff;
}

.footer-separator {
    color: #666666;
    font-size: 0.875rem;
    user-select: none;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modals);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.show .modal-container {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
}

.modal-header h2 {
    font-family: 'Cambria', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c2c2c;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: #666666;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background-color: #e0e0e0;
    color: #2c2c2c;
}

.modal-content {
    padding: 2rem;
    max-height: 70vh;
    overflow-y: auto;
}

/* Release Notes Modal */
.release-notes-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid #e0e0e0;
}

.tab-button {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    color: #666666;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-button.active {
    color: #2c2c2c;
    border-bottom-color: #2c2c2c;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.release-note {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
}

.release-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.release-header h3 {
    font-family: 'Cambria', serif;
    font-size: 1.2rem;
    color: #2c2c2c;
    margin: 0;
}

.release-date {
    font-size: 0.875rem;
    color: #666666;
}

.release-body h4 {
    font-family: 'Cambria', serif;
    font-size: 1rem;
    color: #2c2c2c;
    margin: 1rem 0 0.5rem 0;
}

.release-body ul {
    margin: 0 0 1rem 1.5rem;
    color: #555555;
}

.release-body li {
    margin-bottom: 0.25rem;
}

/* Customization Modal */
.customize-section {
    margin-bottom: 2rem;
}

.customize-section h3 {
    font-family: 'Cambria', serif;
    font-size: 1.1rem;
    color: #2c2c2c;
    margin-bottom: 1rem;
}

.layout-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.layout-option {
    text-align: center;
    cursor: pointer;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.layout-option:hover {
    border-color: #cccccc;
}

.layout-option.active {
    border-color: #2c2c2c;
    background-color: #f8f9fa;
}

.layout-preview {
    margin-bottom: 0.5rem;
}

.preview-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2px;
    height: 60px;
    background-color: #f0f0f0;
    border-radius: 4px;
    padding: 4px;
}

.preview-grid.compact {
    grid-template-columns: 1fr 1fr 1fr;
}

.preview-grid.wide {
    grid-template-columns: 1fr;
}

.preview-card {
    background-color: #2c2c2c;
    border-radius: 2px;
}

.preview-card.small {
    height: 15px;
}

.preview-card.wide {
    height: 25px;
}

.widget-toggles {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.widget-toggle {
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
}

.toggle-slider {
    position: relative;
    width: 50px;
    height: 24px;
    background-color: #cccccc;
    border-radius: 12px;
    transition: background-color 0.3s ease;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background-color: #ffffff;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.widget-toggle input[type="checkbox"] {
    display: none;
}

.widget-toggle input[type="checkbox"]:checked + .toggle-slider {
    background-color: #2c2c2c;
}

.widget-toggle input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(26px);
}

.toggle-label {
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    color: #2c2c2c;
}

.theme-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
}

.theme-option {
    text-align: center;
    cursor: pointer;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.theme-option:hover {
    border-color: #cccccc;
}

.theme-option.active {
    border-color: #2c2c2c;
    background-color: #f8f9fa;
}

.theme-preview {
    width: 60px;
    height: 40px;
    border-radius: 4px;
    margin: 0 auto 0.5rem;
}

.theme-preview.default {
    background: linear-gradient(135deg, #ffffff 50%, #f8f9fa 50%);
    border: 1px solid #e0e0e0;
}

.theme-preview.dark {
    background: linear-gradient(135deg, #2c2c2c 50%, #000000 50%);
}

.theme-preview.blue {
    background: linear-gradient(135deg, #1976d2 50%, #0d47a1 50%);
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.btn-primary, .btn-secondary {
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background-color: #2c2c2c;
    color: #ffffff;
}

.btn-primary:hover {
    background-color: #1a1a1a;
}

.btn-secondary {
    background-color: transparent;
    color: #666666;
    border: 1px solid #cccccc;
}

.btn-secondary:hover {
    background-color: #f8f9fa;
    border-color: #999999;
}

/* Month-End Modal */
.month-end-status h3 {
    font-family: 'Cambria', serif;
    font-size: 1.1rem;
    color: #2c2c2c;
    margin-bottom: 1.5rem;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: 8px;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
}

.status-item.completed {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.status-item.in-progress {
    background-color: #fff3e0;
    color: #f57c00;
}

.status-item.pending {
    background-color: #f5f5f5;
    color: #666666;
}

.month-end-actions h3 {
    font-family: 'Cambria', serif;
    font-size: 1.1rem;
    color: #2c2c2c;
    margin-bottom: 1.5rem;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem 1rem;
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    color: #2c2c2c;
}

.action-btn:hover {
    background-color: #e9ecef;
    border-color: #cccccc;
    transform: translateY(-2px);
}

.action-btn .material-icons {
    font-size: 24px;
    color: #666666;
}

/* Notification System - Removed */

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
.dropdown-item:focus,
.header-button:focus,
.dropdown-trigger:focus,
.search-input:focus,
.nav-link:focus {
    outline: 2px solid #2196f3;
    outline-offset: 2px;
}

.modal-close:focus {
    outline: 2px solid #2196f3;
    outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-content {
        gap: 1rem;
    }

    .header-center {
        max-width: 350px;
    }

    .search-filters {
        display: none;
    }

    .advanced-search-input {
        padding-right: 3rem;
    }

    .nav-container {
        padding: 0 1rem;
    }

    .nav-link {
        padding: 0.75rem 1rem;
    }




}

@media (max-width: 768px) {
    .dashboard-header {
        padding: 0.5rem 0;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
        padding: 0 1rem;
    }

    .header-left {
        justify-content: center;
        order: 1;
    }

    .brand-section {
        padding: 0.5rem 1rem;
        gap: 0.75rem;
    }

    .brand-title {
        font-size: 1.5rem;
    }

    .brand-subtitle {
        font-size: 0.65rem;
    }

    .header-center {
        order: 3;
        max-width: none;
        flex: 1 1 auto;
    }

    .header-right {
        order: 2;
        justify-content: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    /* Branch Selector Mobile */
    .branch-selector-button {
        min-width: 120px;
        padding: 0.5rem 0.75rem;
    }

    .branch-info {
        display: none;
    }

    /* Control Buttons Mobile */
    .control-buttons-group {
        gap: 0.25rem;
        padding: 0.25rem;
    }

    .control-button {
        width: 38px;
        height: 38px;
    }

    /* User Section Mobile */
    .user-section {
        gap: 0.75rem;
        padding-left: 0.75rem;
    }

    .user-display {
        padding: 0.5rem 0.75rem;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
    }

    .user-avatar .material-icons {
        font-size: 18px;
    }

    .logout-button {
        width: 40px;
        height: 40px;
    }

    .logout-button .material-icons {
        font-size: 18px;
    }

    /* Search Results Mobile */
    .search-results {
        top: 100px;
        width: 95%;
        max-width: none;
        max-height: 75vh;
    }

    .search-results-header {
        padding: 1rem;
    }

    .search-results-content {
        padding: 1rem;
        max-height: calc(75vh - 80px);
    }

    .search-item {
        padding: 0.75rem;
        gap: 0.75rem;
    }

    .search-item-icon {
        width: 35px;
        height: 35px;
    }

    .search-item-icon .material-icons {
        font-size: 18px;
    }

    /* Branch Dropdown Mobile */
    .branch-menu {
        min-width: 350px;
        max-width: 95vw;
        left: 50%;
        right: auto;
        transform: translateX(-50%);
    }

    .branch-search-container {
        padding: 1rem 1.25rem;
    }

    .branch-search-input {
        padding: 0.75rem 1rem 0.75rem 2.5rem;
        font-size: 0.9rem;
    }

    .branch-search-icon {
        left: 2rem;
        font-size: 18px;
    }



    /* Horizontal Navigation - REMOVED */

    /* Content adjustments */
    .content-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .main-content {
        min-height: calc(100vh - 180px);
    }

    .icon-button {
        width: 36px;
        height: 36px;
    }

    .icon-button .material-icons {
        font-size: 18px;
    }

    .user-display {
        padding: 0.4rem 0.6rem;
    }

    .user-name {
        font-size: 0.8rem;
    }

    .search-wrapper {
        border-radius: 20px;
    }

    .advanced-search-input {
        padding: 0.6rem 2.5rem 0.6rem 2.5rem;
        font-size: 0.9rem;
    }

    .search-icon {
        left: 0.75rem;
        font-size: 18px;
    }

    .search-clear {
        right: 0.6rem;
    }

    .filter-btn {
        width: 24px;
        height: 24px;
        font-size: 14px;
    }

    [data-tooltip]:hover::before,
    [data-tooltip]:hover::after {
        display: none;
    }

    .modal-container {
        max-width: 95vw;
        margin: 1rem;
    }

    .modal-content {
        padding: 1rem;
    }



    .layout-options,
    .theme-options {
        grid-template-columns: repeat(2, 1fr);
    }

    .action-buttons {
        grid-template-columns: 1fr;
    }

    .status-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .header-right {
        gap: 0.25rem;
    }

    .icon-button {
        width: 32px;
        height: 32px;
    }

    .icon-button .material-icons {
        font-size: 16px;
    }

    .user-display {
        padding: 0.3rem 0.5rem;
    }

    .user-name {
        font-size: 0.75rem;
    }

    /* Search Results Very Small Mobile */
    .search-results {
        top: 80px;
        width: 98%;
        max-height: 80vh;
        border-radius: 12px;
    }

    .search-results-header {
        padding: 0.75rem;
    }

    .search-results-content {
        padding: 0.75rem;
        max-height: calc(80vh - 70px);
    }

    .search-item {
        padding: 0.5rem;
        gap: 0.5rem;
    }

    .search-item-icon {
        width: 30px;
        height: 30px;
    }

    .search-item-icon .material-icons {
        font-size: 16px;
    }

    .search-item-title {
        font-size: 0.8rem;
    }

    .search-item-description {
        font-size: 0.75rem;
    }

    /* Branch Dropdown Very Small Mobile */
    .branch-menu {
        min-width: 320px;
        max-width: 98vw;
    }

    .branch-search-input {
        padding: 0.75rem 1rem 0.75rem 2.25rem;
        font-size: 0.85rem;
    }

    .branch-menu .dropdown-item {
        padding: 1rem 1.25rem;
        min-height: 55px;
    }

    .branch-item-name {
        font-size: 0.9rem;
    }

    .branch-item-code {
        font-size: 0.8rem;
    }



    .notification-badge {
        top: -4px;
        right: -4px;
        font-size: 0.6rem;
        padding: 1px 4px;
    }

    /* Navigation adjustments */
    .nav-link {
        padding: 0.5rem 0.75rem;
    }

    .nav-link .material-icons {
        font-size: 18px;
    }




}



    /* Footer responsive adjustments */
    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .footer-left,
    .footer-center,
    .footer-right {
        justify-content: center;
        flex-wrap: wrap;
    }

    .footer-center {
        order: 3;
    }

    .footer-right {
        order: 2;
    }
}



@media (max-width: 480px) {
    .header-logo {
        height: 30px;
    }

    .header-title h1 {
        font-size: 1.2rem;
    }

    .page-title {
        font-size: 1.5rem;
    }



    .nav-section {
        padding: 0 1rem;
    }

    /* Footer mobile adjustments */
    .dashboard-footer {
        padding: 0.75rem 1rem;
    }

    .footer-content {
        gap: 0.75rem;
    }

    .footer-left,
    .footer-center,
    .footer-right {
        gap: 0.5rem;
        font-size: 0.8rem;
    }

    .footer-version,
    .footer-build {
        font-size: 0.75rem;
    }
}



/* Month-End Modal Styles */
.month-end-section {
    padding: 1rem 0;
}

.section-description {
    color: #666666;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
    line-height: 1.5;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem 1rem;
    border: 2px solid transparent;
    border-radius: 8px;
    background-color: #f8f9fa;
    color: #2c2c2c;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-family: 'Cambria', serif;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-btn.primary {
    background-color: #e3f2fd;
    border-color: #1976d2;
}

.action-btn.primary:hover {
    background-color: #bbdefb;
}

.action-btn.secondary {
    background-color: #f3e5f5;
    border-color: #7b1fa2;
}

.action-btn.secondary:hover {
    background-color: #e1bee7;
}

.action-btn.warning {
    background-color: #fff3e0;
    border-color: #f57c00;
}

.action-btn.warning:hover {
    background-color: #ffe0b2;
}

.action-btn.info {
    background-color: #e0f2f1;
    border-color: #00796b;
}

.action-btn.info:hover {
    background-color: #b2dfdb;
}

.action-btn .material-icons {
    font-size: 32px;
    margin-bottom: 0.5rem;
}

.action-text {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.action-description {
    font-size: 0.85rem;
    color: #666666;
    line-height: 1.4;
}

.month-end-status {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
}

.month-end-status h4 {
    margin-bottom: 1rem;
    color: #2c2c2c;
    font-size: 1.1rem;
}

.status-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.status-item.completed {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.status-item.completed .material-icons {
    color: #4caf50;
}

.status-item.pending {
    background-color: #fff3e0;
    color: #ef6c00;
}

.status-item.pending .material-icons {
    color: #ff9800;
}

/* Enhanced Customization Modal Styles */
.customize-section {
    margin-bottom: 2rem;
}

.customize-section h3 {
    margin-bottom: 1rem;
    color: #2c2c2c;
    font-size: 1.1rem;
}

.layout-options,
.theme-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.layout-option,
.theme-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.layout-option:hover,
.theme-option:hover {
    border-color: #1976d2;
    background-color: #f8f9fa;
}

.layout-option.active,
.theme-option.active {
    border-color: #1976d2;
    background-color: #e3f2fd;
}

.layout-preview,
.theme-preview {
    width: 60px;
    height: 40px;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.preview-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2px;
    height: 100%;
    padding: 2px;
}

.preview-grid.compact {
    grid-template-columns: 1fr 1fr 1fr;
}

.preview-grid.wide {
    grid-template-columns: 1fr;
}

.preview-card {
    background-color: #ddd;
    border-radius: 2px;
}

.preview-card.small {
    background-color: #ccc;
}

.preview-card.wide {
    background-color: #bbb;
}

.theme-preview.default {
    background: linear-gradient(135deg, #ffffff, #f5f5f5);
}

.theme-preview.dark {
    background: linear-gradient(135deg, #333333, #1a1a1a);
}

.theme-preview.blue {
    background: linear-gradient(135deg, #e3f2fd, #1976d2);
}

.widget-toggles {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.widget-toggle {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
}

.toggle-slider {
    position: relative;
    width: 44px;
    height: 24px;
    background-color: #ccc;
    border-radius: 12px;
    transition: background-color 0.3s ease;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background-color: #ffffff;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.widget-toggle input:checked + .toggle-slider {
    background-color: #1976d2;
}

.widget-toggle input:checked + .toggle-slider::before {
    transform: translateX(20px);
}

.widget-toggle input {
    display: none;
}

.toggle-label {
    font-size: 0.9rem;
    color: #2c2c2c;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.btn-primary,
.btn-secondary {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #1976d2;
    color: #ffffff;
}

.btn-primary:hover {
    background-color: #1565c0;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #f5f5f5;
    color: #2c2c2c;
    border: 1px solid #e0e0e0;
}

.btn-secondary:hover {
    background-color: #eeeeee;
    transform: translateY(-1px);
}

/* Modern Utility Classes */
.glass-effect {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-glow {
    box-shadow: var(--shadow-xl), 0 0 40px rgba(14, 165, 233, 0.2);
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}



/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ========================================
   INTEGRATED SEARCH BAR STYLES
   ======================================== */

/* Search Results Dropdown */
.search-results {
    position: fixed;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 600px;
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%) translateY(-10px);
    transition: all 0.3s ease;
    z-index: var(--z-search-results);
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.search-results.active {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
}

.search-results-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    flex-shrink: 0;
}

.search-results-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.results-count {
    font-weight: 600;
    color: #2c2c2c;
}

.search-term {
    color: #666666;
    font-style: italic;
}

.search-results-close {
    background: none;
    border: none;
    color: #999999;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.search-results-close:hover {
    background: #f0f0f0;
    color: #2c2c2c;
}

.search-results-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 0;
}

/* Search Sections */
.search-section {
    margin-bottom: 1.5rem;
}

.search-section:last-child {
    margin-bottom: 0;
}

.search-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1.5rem 0.75rem;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 0.75rem;
}

.search-section-header h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #666666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.clear-recent-btn {
    background: none;
    border: none;
    color: #999999;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.clear-recent-btn:hover {
    background: #f0f0f0;
    color: #2c2c2c;
}

/* Search Items */
.search-items {
    display: flex;
    flex-direction: column;
}

.search-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 1.5rem;
    text-decoration: none;
    color: #2c2c2c;
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.search-item:hover,
.search-item.selected {
    background: #f0f0f0;
    color: #2c2c2c;
}

.search-item.selected {
    background: #000000;
    color: #ffffff;
}

.search-item-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000000;
    color: #ffffff;
    border-radius: 8px;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.search-item-content {
    flex: 1;
    min-width: 0;
}

.search-item-title {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.search-item-description {
    font-size: 0.85rem;
    color: #666666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.search-item-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
    flex-shrink: 0;
}

.search-item-category {
    font-size: 0.75rem;
    color: #999999;
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

/* No Results */
.search-no-results {
    padding: 3rem 1.5rem;
    text-align: center;
}

.no-results-content .material-icons {
    font-size: 3rem;
    color: #999999;
    margin-bottom: 1rem;
}

.no-results-content h4 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: #2c2c2c;
}

.no-results-content p {
    color: #666666;
    margin-bottom: 1.5rem;
}

.search-suggestions h5 {
    font-size: 0.9rem;
    color: #666666;
    margin-bottom: 0.75rem;
}

.suggestion-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

.suggestion-tag {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    color: #2c2c2c;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.suggestion-tag:hover {
    background: #000000;
    color: #ffffff;
    border-color: #000000;
}

/* Search Results Footer */
.search-results-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;
    flex-shrink: 0;
}

.search-navigation-hint {
    display: flex;
    gap: 1rem;
    justify-content: center;
    font-size: 0.8rem;
    color: #999999;
}

.search-navigation-hint kbd {
    background: #999999;
    color: #ffffff;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.7rem;
    font-weight: 500;
}

/* Search Overlay */
.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: calc(var(--z-search-results) - 1);
}

.search-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Responsive Search Styles */
@media (max-width: 768px) {
    .search-container {
        max-width: none;
        margin: 0 1rem;
    }

    .search-input {
        font-size: 0.9rem;
        padding: 0.6rem 0.8rem;
    }

    .search-shortcut {
        display: none;
    }

    .search-results {
        top: 70px;
        width: 95%;
        max-width: none;
        max-height: 70vh;
    }

    .search-results-header {
        padding: 0.75rem 1rem;
    }

    .search-item {
        padding: 0.6rem 1rem;
    }

    .search-item-icon {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }

    .search-item-title {
        font-size: 0.9rem;
    }

    .search-item-description {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .header-center {
        margin: 0 0.5rem;
    }

    .search-container {
        margin: 0;
    }

    .search-input {
        padding: 0.5rem 0.7rem;
    }

    .search-icon {
        margin-left: 0.7rem;
        margin-right: 0.3rem;
    }

    .search-results {
        top: 65px;
        width: 98%;
    }
}

/* Search results styles removed */

/* All search-related styles removed */













/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #2c2c2c;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background: #ffffff;
    color: #2c2c2c;
    font-size: 0.95rem;
    transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #000000;
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: #000000;
    color: #ffffff;
}

.btn-primary:hover {
    background: #333333;
}

.btn-secondary {
    background: #f8f9fa;
    color: #2c2c2c;
    border: 1px solid #e0e0e0;
}

.btn-secondary:hover {
    background: #f0f0f0;
}



/* Responsive Design for Components */
@media (max-width: 768px) {
    /* Menu bar styles removed - no longer needed */
}