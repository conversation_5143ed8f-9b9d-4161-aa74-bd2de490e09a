// Working Dashboard JavaScript
console.log('Dashboard JavaScript loading...');

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded - initializing dashboard');
    initializeDashboard();
});

function initializeDashboard() {
    console.log('Starting dashboard initialization...');

    // Initialize header components
    initializeHeaderButtons();

    // Initialize menu bar
    initializeMenuBar();

    // Initialize search functionality
    initializeSearch();

    console.log('Dashboard initialization complete');
}

// Initialize header buttons
function initializeHeaderButtons() {
    console.log('Initializing header buttons...');

    // Release Notes Button
    const releaseNotesButton = document.getElementById('releaseNotesButton');
    const releaseNotesModal = document.getElementById('releaseNotesModal');
    const closeReleaseNotes = document.getElementById('closeReleaseNotes');

    if (releaseNotesButton && releaseNotesModal && closeReleaseNotes) {
        releaseNotesButton.addEventListener('click', function() {
            console.log('Release notes button clicked');
            showModal(releaseNotesModal);
        });

        closeReleaseNotes.addEventListener('click', function() {
            console.log('Closing release notes modal');
            hideModal(releaseNotesModal);
        });

        console.log('✓ Release notes functionality initialized');
    } else {
        console.warn('Release notes elements not found');
    }

    // Customize Button
    const customizeButton = document.getElementById('customizeButton');
    const customizeModal = document.getElementById('customizeModal');
    const closeCustomize = document.getElementById('closeCustomize');

    if (customizeButton && customizeModal && closeCustomize) {
        customizeButton.addEventListener('click', function() {
            console.log('Customize button clicked');
            showModal(customizeModal);
        });

        closeCustomize.addEventListener('click', function() {
            console.log('Closing customize modal');
            hideModal(customizeModal);
        });

        console.log('✓ Customize functionality initialized');
    } else {
        console.warn('Customize elements not found');
    }

    // Month-End Button
    const monthEndButton = document.getElementById('monthEndButton');
    const monthEndModal = document.getElementById('monthEndModal');
    const closeMonthEnd = document.getElementById('closeMonthEnd');

    if (monthEndButton && monthEndModal && closeMonthEnd) {
        monthEndButton.addEventListener('click', function() {
            console.log('Month-end button clicked');
            showModal(monthEndModal);
        });

        closeMonthEnd.addEventListener('click', function() {
            console.log('Closing month-end modal');
            hideModal(monthEndModal);
        });

        console.log('✓ Month-end functionality initialized');
    } else {
        console.warn('Month-end elements not found');
    }

    // Logout Button
    const logoutButton = document.getElementById('logoutButton');
    if (logoutButton) {
        logoutButton.addEventListener('click', function() {
            console.log('Logout button clicked');
            if (confirm('Are you sure you want to logout?')) {
                console.log('Logout confirmed');
                showNotification('Logging out...', 'info');
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1000);
            }
        });

        console.log('✓ Logout functionality initialized');
    } else {
        console.warn('Logout button not found');
    }

    // Branch Selector
    const branchSelector = document.getElementById('branchSelector');
    const branchMenu = document.getElementById('branchMenu');

    if (branchSelector && branchMenu) {
        branchSelector.addEventListener('click', function() {
            console.log('Branch selector clicked');
            toggleDropdown(branchSelector, branchMenu);
        });

        // Handle branch selection
        const branchItems = branchMenu.querySelectorAll('.dropdown-item');
        branchItems.forEach(item => {
            item.addEventListener('click', function() {
                const branchName = this.querySelector('.branch-item-name').textContent;
                console.log(`Branch selected: ${branchName}`);

                // Update current branch display
                const currentBranchName = document.getElementById('currentBranchName');
                if (currentBranchName) {
                    currentBranchName.textContent = branchName;
                }

                // Update active state
                branchItems.forEach(i => i.classList.remove('active'));
                this.classList.add('active');

                closeDropdown(branchSelector, branchMenu);
                showNotification(`Switched to ${branchName}`, 'success');
            });
        });

        console.log('✓ Branch selector functionality initialized');
    } else {
        console.warn('Branch selector elements not found');
    }
}

// Initialize menu bar
function initializeMenuBar() {
    console.log('Initializing menu bar...');

    const menuItems = document.querySelectorAll('.menu-item');
    const menuBackdrop = document.getElementById('menuBackdrop');

    if (menuItems.length === 0) {
        console.warn('No menu items found');
        return;
    }

    console.log(`Found ${menuItems.length} menu items`);

    menuItems.forEach((menuItem, index) => {
        const menuButton = menuItem.querySelector('.menu-button');
        const megaMenu = menuItem.querySelector('.mega-menu');
        const menuText = menuButton ? menuButton.textContent.trim() : `Menu ${index + 1}`;

        if (!menuButton) {
            console.warn(`No menu button found for menu item ${index + 1}`);
            return;
        }

        menuButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log(`Menu clicked: ${menuText}`);

            if (megaMenu) {
                // Handle mega menu
                const isActive = menuItem.classList.contains('active');

                // Close all other menus
                closeAllMenus();

                if (!isActive) {
                    openMenu(menuItem);
                }
            } else {
                // Handle simple menu
                handleSimpleMenuClick(menuItem, menuText);
            }
        });

        // Add hover functionality for desktop
        if (megaMenu && window.innerWidth > 768) {
            menuItem.addEventListener('mouseenter', function() {
                if (!isMobileDevice()) {
                    openMenu(menuItem);
                }
            });

            menuItem.addEventListener('mouseleave', function() {
                if (!isMobileDevice()) {
                    setTimeout(() => {
                        if (!menuItem.matches(':hover')) {
                            closeMenu(menuItem);
                        }
                    }, 100);
                }
            });
        }

        console.log(`✓ Menu item initialized: ${menuText}`);
    });

    // Initialize tab functionality
    initializeMenuTabs();

    // Initialize menu search
    initializeMenuSearch();

    // Initialize menu links
    initializeMenuLinks();

    // Handle backdrop clicks
    if (menuBackdrop) {
        menuBackdrop.addEventListener('click', closeAllMenus);
    }

    // Handle clicks outside menu
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.menu-item')) {
            closeAllMenus();
        }
    });

    // Handle ESC key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllMenus();
        }
    });

    console.log('✓ Menu bar initialization complete');
}

// Initialize menu tabs
function initializeMenuTabs() {
    const menuTabs = document.querySelectorAll('.menu-tab');

    menuTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            console.log(`Tab clicked: ${targetTab}`);

            // Update tab states
            const tabContainer = this.closest('.menu-tabs');
            const tabs = tabContainer.querySelectorAll('.menu-tab');
            tabs.forEach(t => {
                t.classList.remove('active');
                t.setAttribute('aria-selected', 'false');
            });
            this.classList.add('active');
            this.setAttribute('aria-selected', 'true');

            // Update panels
            const tabContent = tabContainer.parentElement.querySelector('.menu-tab-content');
            if (tabContent) {
                const panels = tabContent.querySelectorAll('.tab-panel');
                panels.forEach(panel => panel.classList.remove('active'));

                const targetPanel = tabContent.querySelector(`#${targetTab}-panel`);
                if (targetPanel) {
                    targetPanel.classList.add('active');
                }
            }

            // Clear search when switching tabs
            const searchInput = tabContainer.parentElement.querySelector('.menu-search-input');
            if (searchInput) {
                searchInput.value = '';
                const megaMenu = searchInput.closest('.mega-menu');
                if (megaMenu) {
                    filterMenuItems(megaMenu, '');
                }
            }
        });
    });

    if (menuTabs.length > 0) {
        console.log(`✓ ${menuTabs.length} menu tabs initialized`);
    }
}

// Initialize menu search
function initializeMenuSearch() {
    const searchInputs = document.querySelectorAll('.menu-search-input');

    searchInputs.forEach(searchInput => {
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase().trim();
            const megaMenu = this.closest('.mega-menu');

            if (megaMenu) {
                filterMenuItems(megaMenu, query);
            }
        });

        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                this.value = '';
                const megaMenu = this.closest('.mega-menu');
                if (megaMenu) {
                    filterMenuItems(megaMenu, '');
                }
                closeAllMenus();
            }
        });

        // Prevent menu from closing when clicking on search input
        searchInput.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });

    if (searchInputs.length > 0) {
        console.log(`✓ ${searchInputs.length} menu search inputs initialized`);
    }
}

// Initialize menu links
function initializeMenuLinks() {
    const menuLinks = document.querySelectorAll('.menu-link');

    menuLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const linkText = this.textContent.trim();
            console.log(`Menu link clicked: ${linkText}`);

            showNotification(`Navigating to ${linkText}`, 'info');
            closeAllMenus();
        });
    });

    if (menuLinks.length > 0) {
        console.log(`✓ ${menuLinks.length} menu links initialized`);
    }
}

// Initialize search functionality
function initializeSearch() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            if (query.length > 2) {
                console.log(`Global search: ${query}`);
                // Implement global search functionality here
                showNotification(`Searching for: ${query}`, 'info');
            }
        });

        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                const query = this.value.trim();
                if (query) {
                    console.log(`Search submitted: ${query}`);
                    showNotification(`Search results for: ${query}`, 'success');
                }
            }
        });

        console.log('✓ Global search initialized');
    }
}

// Utility functions
function showModal(modal) {
    if (modal) {
        modal.classList.add('show');
        modal.setAttribute('aria-hidden', 'false');
        document.body.style.overflow = 'hidden';

        // Focus management
        const firstFocusable = modal.querySelector('button, input, select, textarea, [tabindex]:not([tabindex="-1"])');
        if (firstFocusable) {
            setTimeout(() => firstFocusable.focus(), 100);
        }
    }
}

function hideModal(modal) {
    if (modal) {
        modal.classList.remove('show');
        modal.setAttribute('aria-hidden', 'true');
        document.body.style.overflow = '';
    }
}

function toggleDropdown(trigger, menu) {
    const isOpen = trigger.classList.contains('active');

    // Close all other dropdowns first
    document.querySelectorAll('.dropdown-trigger.active').forEach(t => {
        if (t !== trigger) {
            const m = document.getElementById(t.getAttribute('aria-controls'));
            if (m) closeDropdown(t, m);
        }
    });

    if (isOpen) {
        closeDropdown(trigger, menu);
    } else {
        openDropdown(trigger, menu);
    }
}

function openDropdown(trigger, menu) {
    trigger.classList.add('active');
    trigger.setAttribute('aria-expanded', 'true');
    menu.classList.add('show');
}

function closeDropdown(trigger, menu) {
    trigger.classList.remove('active');
    trigger.setAttribute('aria-expanded', 'false');
    menu.classList.remove('show');
}

function openMenu(menuItem) {
    const menuButton = menuItem.querySelector('.menu-button');
    const menuBackdrop = document.getElementById('menuBackdrop');

    menuItem.classList.add('active');
    menuButton.setAttribute('aria-expanded', 'true');

    if (isMobileDevice() && menuBackdrop) {
        menuBackdrop.classList.add('active');
    }

    // Focus management
    setTimeout(() => {
        const searchInput = menuItem.querySelector('.menu-search-input');
        const firstTab = menuItem.querySelector('.menu-tab');
        const firstLink = menuItem.querySelector('.menu-link');

        if (searchInput) {
            searchInput.focus();
        } else if (firstTab) {
            firstTab.focus();
        } else if (firstLink) {
            firstLink.focus();
        }
    }, 100);

    console.log('Menu opened:', menuButton.textContent.trim());
}

function closeMenu(menuItem) {
    const menuButton = menuItem.querySelector('.menu-button');
    const searchInput = menuItem.querySelector('.menu-search-input');

    menuItem.classList.remove('active');
    menuButton.setAttribute('aria-expanded', 'false');

    // Clear search
    if (searchInput) {
        searchInput.value = '';
        const megaMenu = menuItem.querySelector('.mega-menu');
        if (megaMenu) {
            filterMenuItems(megaMenu, '');
        }
    }
}

function closeAllMenus() {
    const activeMenus = document.querySelectorAll('.menu-item.active');
    const menuBackdrop = document.getElementById('menuBackdrop');

    activeMenus.forEach(closeMenu);

    if (menuBackdrop) {
        menuBackdrop.classList.remove('active');
    }
}

function handleSimpleMenuClick(menuItem, menuText) {
    // Remove active class from all menu items
    document.querySelectorAll('.menu-item').forEach(item => {
        item.classList.remove('active');
    });

    // Add active class to clicked item
    menuItem.classList.add('active');

    console.log(`Navigating to: ${menuText}`);
    showNotification(`Navigating to ${menuText}`, 'info');

    // In a real application, you would navigate to the actual page
    // For now, just show visual feedback
    setTimeout(() => {
        menuItem.classList.remove('active');
    }, 2000);
}

function filterMenuItems(megaMenu, query) {
    const menuLinks = megaMenu.querySelectorAll('.menu-link');
    const menuColumns = megaMenu.querySelectorAll('.menu-column');
    let visibleCount = 0;

    menuLinks.forEach(link => {
        const text = link.textContent.toLowerCase();
        const isVisible = query === '' || text.includes(query);

        if (isVisible) {
            link.style.display = 'flex';
            link.classList.toggle('highlighted', query !== '' && text.includes(query));
            visibleCount++;

            // Highlight matching text
            if (query !== '') {
                highlightSearchText(link, query);
            } else {
                removeSearchHighlight(link);
            }
        } else {
            link.style.display = 'none';
            link.classList.remove('highlighted');
        }
    });

    // Show/hide columns based on visible links
    menuColumns.forEach(column => {
        const visibleLinks = column.querySelectorAll('.menu-link:not([style*="display: none"])');
        column.style.display = visibleLinks.length > 0 ? 'block' : 'none';
    });

    console.log(`Menu search: "${query}" - ${visibleCount} results`);
}

function highlightSearchText(element, query) {
    const textSpan = element.querySelector('span:last-child');
    if (!textSpan) return;

    const originalText = textSpan.textContent;
    const regex = new RegExp(`(${escapeRegExp(query)})`, 'gi');
    const highlightedText = originalText.replace(regex, '<mark class="search-highlight">$1</mark>');

    textSpan.innerHTML = highlightedText;
}

function removeSearchHighlight(element) {
    const textSpan = element.querySelector('span:last-child');
    if (!textSpan) return;

    textSpan.innerHTML = textSpan.textContent;
}

function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function isMobileDevice() {
    return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

function showNotification(message, type = 'info') {
    console.log(`Notification: ${message} (${type})`);

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#22c55e' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        z-index: 10000;
        font-family: 'Roboto', sans-serif;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transform: translateX(100%);
        opacity: 0;
        transition: all 0.3s ease;
        max-width: 300px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    `;

    // Add icon
    const icon = document.createElement('span');
    icon.className = 'material-icons';
    icon.style.fontSize = '20px';
    icon.textContent = type === 'success' ? 'check_circle' : type === 'error' ? 'error' : 'info';

    notification.appendChild(icon);
    notification.appendChild(document.createTextNode(message));

    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
        notification.style.opacity = '1';
    }, 100);

    // Hide notification
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        notification.style.opacity = '0';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

console.log('Dashboard JavaScript loaded successfully');
